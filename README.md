# 🚀 Daily Work Launcher - Professional Edition

A powerful, modern window management tool that revolutionizes your morning routine by automatically opening all your daily work windows with precision timing tracking.

## ✨ Features

### 🎯 Core Features
- **One-Click Launch**: Open all your daily windows with a single click
- **Smart Timing**: Track opening and usage time for each window
- **Modern UI**: Beautiful dark theme with professional interface
- **Multi-Browser Support**: Chrome, Firefox, Edge, or system default
- **Application Support**: Launch any Windows application or website
- **Export Reports**: Generate detailed timing reports
- **Customizable Delays**: Set individual launch delays per window

### 📊 Advanced Analytics
- **Real-time Tracking**: Monitor window usage in real-time
- **Session Analytics**: View total active time and individual window metrics
- **Export Capabilities**: Save timing reports for productivity analysis
- **Visual Progress**: Progress bars and status updates during launch

### 🔧 Professional Tools
- **Configuration Management**: Save/load window configurations
- **Quick Setup**: Pre-defined common sites (Gmail, Slack, Teams, etc.)
- **Test Mode**: Test individual windows before full launch
- **Background Processing**: Non-blocking window launches
- **Error Handling**: Robust error handling with detailed logging

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)
1. Double-click `launch_setup.bat`
2. Wait for automatic package installation
3. The application will start automatically

### Option 2: Manual Setup
1. Install Python packages:
   ```cmd
   pip install psutil requests Pillow
   ```
2. Run the application:
   ```cmd
   python daily_launcher.py
   ```

### Option 3: Quick Launch (After Setup)
- Double-click `start_launcher.bat` for instant access

## 📋 How to Use

### 1. Configure Your Windows
- Go to **"⚙️ Configure Windows"** tab
- Add your daily websites and applications:
  - **URL/Path**: Enter website URL or application path
  - **Name**: Give it a friendly name
  - **Delay**: Set launch delay (seconds)
- Use **"🌐 Add Common Sites"** for quick setup
- Use **"📁 Browse App"** to select applications

### 2. Launch Your Workspace
- Switch to **"🚀 Launch Control"** tab
- Click **"🚀 Launch All Windows"**
- Watch the progress and log updates
- Use **"🔄 Test Single Window"** to test individual entries

### 3. Monitor Usage
- Check **"⏱️ Timing Dashboard"** tab
- View real-time usage statistics
- Export reports for productivity analysis
- Clear history when needed

### 4. Customize Settings
- Visit **"⚙️ Settings"** tab
- Set default browser preference
- Configure auto-start options
- Adjust default launch delays

## 🌐 Pre-Configured Common Sites

The tool includes quick-add options for popular work sites:
- **Email**: Gmail, Outlook
- **Communication**: Slack, Teams, WhatsApp Web
- **Development**: GitHub
- **Productivity**: Google Drive, Notion, Trello
- **Professional**: LinkedIn

## 📊 Example Daily Workflow

### Morning Setup (5 seconds total):
1. Arrive at office
2. Double-click `start_launcher.bat`
3. Click "🚀 Launch All Windows"
4. All your work windows open automatically

### Typical Configuration:
```
🌐 Gmail (0s delay)
💬 Slack (2s delay)
📝 Notion (2s delay)
💻 GitHub (2s delay)
📊 Company Dashboard (3s delay)
🗂️ Google Drive (2s delay)
```

## 🔧 Advanced Features

### Smart Browser Handling
- Automatically detects installed browsers
- Supports Chrome, Firefox, Edge
- Falls back to system default
- Option to open in new windows

### Timing Analytics
- Tracks exact launch times
- Calculates session duration
- Monitors individual window usage
- Exports detailed reports

### Error Recovery
- Handles missing applications gracefully
- Provides detailed error logging
- Continues launching remaining windows
- Shows clear status updates

## 📁 File Structure

```
daily_launcher.py      # Main application
launch_setup.bat       # Automatic setup script
start_launcher.bat     # Quick launch script
requirements.txt       # Python dependencies
README.md             # This documentation
workspace_config.json # Your saved configuration (auto-generated)
```

## 🛠️ Troubleshooting

### Common Issues:

**Application won't start:**
- Run `launch_setup.bat` to install dependencies
- Ensure Python 3.7+ is installed

**Browser doesn't open correctly:**
- Check browser settings in Settings tab
- Try "System Default" browser option

**Application path not found:**
- Use "📁 Browse App" to select correct executable
- Ensure full path is specified

**Timing not updating:**
- Check if psutil package is installed
- Restart the application

### Performance Tips:

- **Optimal Delays**: Use 2-3 second delays between launches
- **Resource Management**: Close unused windows to improve performance
- **Regular Cleanup**: Clear timing history weekly
- **Configuration Backup**: Export/save configurations regularly

## 💡 Pro Tips

1. **Morning Efficiency**: Set up everything once, use daily
2. **Delay Optimization**: Heavier applications need longer delays
3. **Browser Tabs**: Consider using bookmarks for multiple related sites
4. **Application Integration**: Add your IDE, terminal, and productivity tools
5. **Report Analysis**: Use timing reports to optimize your workflow

## 🎯 Perfect For:

- **Developers**: GitHub, IDE, terminals, documentation
- **Designers**: Design tools, inspiration sites, project management
- **Managers**: Email, calendar, team communication, dashboards
- **Sales**: CRM, email, LinkedIn, company tools
- **Students**: Learning platforms, research sites, note-taking tools

## 🔮 Future Enhancements

Potential upcoming features:
- Window positioning and sizing
- Multiple workspace profiles
- Integration with calendar systems
- AI-powered usage optimization
- Cloud configuration sync
- Mobile companion app

## 📞 Support

For issues or suggestions:
1. Check the troubleshooting section
2. Review error logs in the Launch Control tab
3. Ensure all dependencies are installed
4. Try running `launch_setup.bat` again

---

**🚀 Transform your morning routine from chaotic to systematic!**

*Built with Python & Tkinter for maximum compatibility and performance.*
