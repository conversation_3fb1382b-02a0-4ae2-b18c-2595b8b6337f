#!/usr/bin/env python3
"""
🚀 Daily Work Launcher - Lite Edition
Zero-dependency version for immediate use
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import webbrowser
import subprocess
import threading
import time
import json
import os
from datetime import datetime, timedelta
from urllib.parse import urlparse

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Daily Work Launcher - Lite")
        self.root.geometry("800x600")
        self.root.configure(bg='#1e1e1e')
        
        # Data storage
        self.config_file = "simple_config.json"
        self.windows_data = []
        self.timing_data = []
        self.start_time = None
        
        # Load saved configuration
        self.load_config()
        
        # Create UI
        self.create_ui()
    
    def create_ui(self):
        """Create simplified user interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, 
                              text="🚀 Daily Work Launcher - Lite Edition", 
                              bg='#1e1e1e', fg='#ffffff', 
                              font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Input section
        self.create_input_section(main_frame)
        
        # Windows list
        self.create_windows_list(main_frame)
        
        # Control buttons
        self.create_control_buttons(main_frame)
        
        # Status and log
        self.create_status_section(main_frame)
    
    def create_input_section(self, parent):
        """Create input section"""
        input_frame = tk.Frame(parent, bg='#2d2d2d', relief='ridge', bd=2)
        input_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(input_frame, text="Add New Window", 
                bg='#2d2d2d', fg='#00d4aa', 
                font=('Segoe UI', 12, 'bold')).pack(pady=10)
        
        # URL input
        url_frame = tk.Frame(input_frame, bg='#2d2d2d')
        url_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(url_frame, text="URL or App Path:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.url_entry = tk.Entry(url_frame, width=70, font=('Segoe UI', 10))
        self.url_entry.pack(fill='x', pady=2)
        
        # Name input
        name_frame = tk.Frame(input_frame, bg='#2d2d2d')
        name_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(name_frame, text="Window Name:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.name_entry = tk.Entry(name_frame, width=40, font=('Segoe UI', 10))
        self.name_entry.pack(anchor='w', pady=2)
        
        # Buttons
        button_frame = tk.Frame(input_frame, bg='#2d2d2d')
        button_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(button_frame, text="➕ Add Window", 
                 command=self.add_window, 
                 bg='#107c10', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="📁 Browse", 
                 command=self.browse_application, 
                 bg='#0078d4', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🌐 Quick Sites", 
                 command=self.add_quick_sites, 
                 bg='#0078d4', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='left', padx=5)
    
    def create_windows_list(self, parent):
        """Create windows list section"""
        list_frame = tk.Frame(parent, bg='#2d2d2d', relief='ridge', bd=2)
        list_frame.pack(fill='both', expand=True, pady=10)
        
        tk.Label(list_frame, text="Configured Windows", 
                bg='#2d2d2d', fg='#00d4aa', 
                font=('Segoe UI', 12, 'bold')).pack(pady=10)
        
        # Listbox with scrollbar
        listbox_frame = tk.Frame(list_frame, bg='#2d2d2d')
        listbox_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.windows_listbox = tk.Listbox(listbox_frame, 
                                         bg='#1e1e1e', fg='#ffffff',
                                         font=('Consolas', 10),
                                         selectbackground='#0078d4')
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', 
                                command=self.windows_listbox.yview)
        self.windows_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.windows_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # List control buttons
        list_buttons = tk.Frame(list_frame, bg='#2d2d2d')
        list_buttons.pack(fill='x', padx=20, pady=10)
        
        tk.Button(list_buttons, text="🗑️ Remove", 
                 command=self.remove_window, 
                 bg='#d13438', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='left', padx=5)
        
        tk.Button(list_buttons, text="💾 Save Config", 
                 command=self.save_config, 
                 bg='#107c10', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='right', padx=5)
    
    def create_control_buttons(self, parent):
        """Create main control buttons"""
        control_frame = tk.Frame(parent, bg='#1e1e1e')
        control_frame.pack(fill='x', pady=10)
        
        # Main launch button
        tk.Button(control_frame, text="🚀 LAUNCH ALL WINDOWS", 
                 command=self.launch_all_windows, 
                 bg='#107c10', fg='white', 
                 font=('Segoe UI', 14, 'bold'),
                 height=2).pack(side='left', padx=10, fill='x', expand=True)
        
        # Secondary buttons
        secondary_frame = tk.Frame(control_frame, bg='#1e1e1e')
        secondary_frame.pack(side='right')
        
        tk.Button(secondary_frame, text="🧪 Test Selected", 
                 command=self.test_selected, 
                 bg='#0078d4', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='top', pady=2)
        
        tk.Button(secondary_frame, text="📊 View Timing", 
                 command=self.show_timing, 
                 bg='#0078d4', fg='white', 
                 font=('Segoe UI', 10, 'bold')).pack(side='top', pady=2)
    
    def create_status_section(self, parent):
        """Create status and log section"""
        status_frame = tk.Frame(parent, bg='#2d2d2d', relief='ridge', bd=2)
        status_frame.pack(fill='x', pady=10)
        
        # Status label
        self.status_label = tk.Label(status_frame, 
                                    text="Ready to launch windows", 
                                    bg='#2d2d2d', fg='#00d4aa', 
                                    font=('Segoe UI', 12, 'bold'))
        self.status_label.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(status_frame, length=400, mode='determinate')
        self.progress.pack(pady=5)
        
        # Log area
        tk.Label(status_frame, text="Activity Log:", 
                bg='#2d2d2d', fg='#ffffff', 
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', padx=20, pady=(10,0))
        
        log_frame = tk.Frame(status_frame, bg='#2d2d2d')
        log_frame.pack(fill='x', padx=20, pady=10)
        
        self.log_text = tk.Text(log_frame, height=6, 
                               bg='#1e1e1e', fg='#ffffff', 
                               font=('Consolas', 9))
        log_scrollbar = tk.Scrollbar(log_frame, orient='vertical', 
                                    command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')
    
    def add_window(self):
        """Add a new window to the configuration"""
        url = self.url_entry.get().strip()
        name = self.name_entry.get().strip()
        
        if not url or not name:
            messagebox.showerror("Error", "Please enter both URL/Path and Name")
            return
        
        window_config = {
            'name': name,
            'url': url,
            'type': 'url' if url.startswith(('http://', 'https://')) else 'app'
        }
        
        self.windows_data.append(window_config)
        self.refresh_windows_list()
        
        # Clear inputs
        self.url_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        
        self.log_message(f"✅ Added: {name}")
    
    def browse_application(self):
        """Browse for an application"""
        file_path = filedialog.askopenfilename(
            title="Select Application",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, file_path)
    
    def add_quick_sites(self):
        """Add quick common sites"""
        sites = [
            ("Gmail", "https://mail.google.com"),
            ("Outlook", "https://outlook.live.com"),
            ("WhatsApp Web", "https://web.whatsapp.com"),
            ("Slack", "https://slack.com"),
            ("Teams", "https://teams.microsoft.com"),
            ("GitHub", "https://github.com"),
            ("LinkedIn", "https://linkedin.com")
        ]
        
        for name, url in sites:
            if not any(w['name'] == name for w in self.windows_data):
                self.windows_data.append({
                    'name': name,
                    'url': url,
                    'type': 'url'
                })
        
        self.refresh_windows_list()
        self.log_message("🌐 Added common sites")
    
    def remove_window(self):
        """Remove selected window"""
        selection = self.windows_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a window to remove")
            return
        
        index = selection[0]
        removed = self.windows_data.pop(index)
        self.refresh_windows_list()
        self.log_message(f"🗑️ Removed: {removed['name']}")
    
    def refresh_windows_list(self):
        """Refresh the windows list display"""
        self.windows_listbox.delete(0, tk.END)
        for window in self.windows_data:
            display_text = f"{window['name']} → {window['url'][:50]}{'...' if len(window['url']) > 50 else ''}"
            self.windows_listbox.insert(tk.END, display_text)
    
    def launch_all_windows(self):
        """Launch all configured windows"""
        if not self.windows_data:
            messagebox.showwarning("Warning", "No windows configured")
            return
        
        self.log_message("🚀 Starting launch sequence...")
        self.progress['maximum'] = len(self.windows_data)
        self.progress['value'] = 0
        self.start_time = datetime.now()
        
        # Launch in separate thread
        threading.Thread(target=self._launch_thread, daemon=True).start()
    
    def _launch_thread(self):
        """Launch windows in background thread"""
        for i, window in enumerate(self.windows_data):
            try:
                self.root.after(0, lambda w=window: self.status_label.config(text=f"Launching: {w['name']}"))
                
                if window['type'] == 'url':
                    webbrowser.open(window['url'])
                else:
                    subprocess.Popen([window['url']])
                
                # Track timing
                self.timing_data.append({
                    'name': window['name'],
                    'time': datetime.now().strftime("%H:%M:%S")
                })
                
                self.log_message(f"✅ Launched: {window['name']}")
                self.root.after(0, lambda: self.progress.config(value=i+1))
                
                # Small delay between launches
                time.sleep(2)
                
            except Exception as e:
                self.log_message(f"❌ Failed: {window['name']} - {str(e)}")
        
        total_time = (datetime.now() - self.start_time).seconds
        self.root.after(0, lambda: self.status_label.config(text=f"✅ Completed in {total_time}s"))
        self.log_message(f"🎉 All windows launched in {total_time} seconds")
    
    def test_selected(self):
        """Test launch selected window"""
        selection = self.windows_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a window to test")
            return
        
        window = self.windows_data[selection[0]]
        try:
            if window['type'] == 'url':
                webbrowser.open(window['url'])
            else:
                subprocess.Popen([window['url']])
            
            self.log_message(f"🧪 Test launched: {window['name']}")
        except Exception as e:
            self.log_message(f"❌ Test failed: {window['name']} - {str(e)}")
    
    def show_timing(self):
        """Show timing information"""
        if not self.timing_data:
            messagebox.showinfo("Info", "No timing data available")
            return
        
        # Create timing window
        timing_window = tk.Toplevel(self.root)
        timing_window.title("📊 Timing Report")
        timing_window.geometry("400x300")
        timing_window.configure(bg='#2d2d2d')
        
        tk.Label(timing_window, text="📊 Launch Timing Report", 
                bg='#2d2d2d', fg='#ffffff', 
                font=('Segoe UI', 12, 'bold')).pack(pady=10)
        
        if self.start_time:
            total_time = (datetime.now() - self.start_time).seconds
            tk.Label(timing_window, text=f"Session Duration: {total_time} seconds", 
                    bg='#2d2d2d', fg='#00d4aa', 
                    font=('Segoe UI', 10)).pack()
        
        # Timing list
        timing_list = tk.Listbox(timing_window, bg='#1e1e1e', fg='#ffffff')
        timing_list.pack(fill='both', expand=True, padx=20, pady=10)
        
        for timing in self.timing_data:
            timing_list.insert(tk.END, f"{timing['time']} - {timing['name']}")
    
    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def save_config(self):
        """Save configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.windows_data, f, indent=2)
            
            messagebox.showinfo("Success", "Configuration saved!")
            self.log_message("💾 Configuration saved")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save: {str(e)}")
    
    def load_config(self):
        """Load configuration"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self.windows_data = json.load(f)
            except Exception as e:
                self.log_message(f"⚠️ Failed to load config: {str(e)}")
    
    def run(self):
        """Run the application"""
        # Initial setup
        self.refresh_windows_list()
        self.log_message("🚀 Daily Work Launcher - Lite Edition started")
        self.log_message("💡 Add windows and click 'Launch All Windows' to begin")
        
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleLauncher()
    app.run()
