{"ast": null, "code": "/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M14.837 16.385a6 6 0 1 1-7.223-7.222c.624-.147.97.66.715 1.248a4 4 0 0 0 5.26 5.259c.589-.255 1.396.09 1.248.715\",\n  key: \"xlf6rm\"\n}], [\"path\", {\n  d: \"M16 12a4 4 0 0 0-4-4\",\n  key: \"6vsxu\"\n}], [\"path\", {\n  d: \"m19 5-1.256 1.256\",\n  key: \"1yg6a6\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}]];\nconst SunMoon = createLucideIcon(\"sun-moon\", __iconNode);\nexport { __iconNode, SunMoon as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SunMoon", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\node_modules\\lucide-react\\src\\icons\\sun-moon.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  [\n    'path',\n    {\n      d: 'M14.837 16.385a6 6 0 1 1-7.223-7.222c.624-.147.97.66.715 1.248a4 4 0 0 0 5.26 5.259c.589-.255 1.396.09 1.248.715',\n      key: 'xlf6rm',\n    },\n  ],\n  ['path', { d: 'M16 12a4 4 0 0 0-4-4', key: '6vsxu' }],\n  ['path', { d: 'm19 5-1.256 1.256', key: '1yg6a6' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n];\n\n/**\n * @component @name SunMoon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYyIiAvPgogIDxwYXRoIGQ9Ik0xNC44MzcgMTYuMzg1YTYgNiAwIDEgMS03LjIyMy03LjIyMmMuNjI0LS4xNDcuOTcuNjYuNzE1IDEuMjQ4YTQgNCAwIDAgMCA1LjI2IDUuMjU5Yy41ODktLjI1NSAxLjM5Ni4wOSAxLjI0OC43MTUiIC8+CiAgPHBhdGggZD0iTTE2IDEyYTQgNCAwIDAgMC00LTQiIC8+CiAgPHBhdGggZD0ibTE5IDUtMS4yNTYgMS4yNTYiIC8+CiAgPHBhdGggZD0iTTIwIDEyaDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun-moon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SunMoon = createLucideIcon('sun-moon', __iconNode);\n\nexport default SunMoon;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAS,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}