"""
Simplified Morning Office Automation Tool - Backend API
FastAPI application without external dependencies for window management
"""

import json
import asyncio
import webbrowser
import subprocess
import platform
import time
from datetime import datetime
from typing import List, Dict, Any
import os
import sys
import uuid
from pathlib import Path

# Simple HTTP server for API
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import threading

class AutomationAPI(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {"message": "Morning Office Automation API is running!"}
            self.wfile.write(json.dumps(response).encode())
        
        elif self.path == '/api/config':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            config = self.load_config()
            self.wfile.write(json.dumps(config).encode())
        
        elif self.path == '/api/urls':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            config = self.load_config()
            urls = config.get('urls', [])
            self.wfile.write(json.dumps(urls).encode())
        
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        if self.path == '/api/launch':
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            # Launch the morning routine
            result = self.launch_routine()
            self.wfile.write(json.dumps(result).encode())
        
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def load_config(self):
        """Load configuration from file"""
        config_file = Path(__file__).parent.parent / "config" / "settings.json"
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                "urls": [
                    {"id": "1", "name": "Gmail", "url": "https://mail.google.com", "category": "Email", "enabled": True},
                    {"id": "2", "name": "GitHub", "url": "https://github.com", "category": "Development", "enabled": True},
                    {"id": "3", "name": "Slack", "url": "https://slack.com", "category": "Communication", "enabled": True}
                ],
                "applications": [
                    {"id": "1", "name": "VS Code", "path": "code", "category": "Development", "enabled": True},
                    {"id": "2", "name": "Terminal", "path": "wt", "category": "Development", "enabled": True}
                ],
                "preferences": {"window_delay": 2}
            }
    
    def launch_routine(self):
        """Launch the morning routine"""
        try:
            config = self.load_config()
            urls = [url for url in config.get('urls', []) if url.get('enabled', True)]
            applications = [app for app in config.get('applications', []) if app.get('enabled', True)]
            
            session_id = str(uuid.uuid4())
            
            # Launch URLs
            for url_config in urls:
                url = url_config.get('url')
                name = url_config.get('name', 'Unknown')
                print(f"🌐 Opening {name}: {url}")
                webbrowser.open(url)
                time.sleep(2)  # Delay between launches
            
            # Launch applications
            for app_config in applications:
                path = app_config.get('path')
                name = app_config.get('name', 'Unknown')
                print(f"🚀 Launching {name}: {path}")
                try:
                    if platform.system() == "Windows":
                        subprocess.Popen(path, shell=True)
                    else:
                        subprocess.Popen([path])
                    time.sleep(2)  # Delay between launches
                except Exception as e:
                    print(f"Error launching {name}: {e}")
            
            return {
                "message": "Morning routine launched successfully",
                "session_id": session_id,
                "urls_count": len(urls),
                "apps_count": len(applications),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "error": str(e),
                "message": "Failed to launch routine"
            }

def run_server():
    """Run the HTTP server"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, AutomationAPI)
    print("🚀 Morning Office Automation API Server")
    print("📊 Server running on http://localhost:8000")
    print("🔗 API endpoints:")
    print("   GET  /api/config - Get configuration")
    print("   GET  /api/urls - Get URLs")
    print("   POST /api/launch - Launch morning routine")
    print("\n💡 To test the API:")
    print("   curl http://localhost:8000/api/config")
    print("   curl -X POST http://localhost:8000/api/launch")
    print("\n⏹️  Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
