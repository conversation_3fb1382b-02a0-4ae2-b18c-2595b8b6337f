#!/usr/bin/env python3
"""
Morning Office Automation - Startup Script
This script provides multiple ways to run the automation tool
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Print the application banner"""
    print("🌅" + "=" * 60 + "🌅")
    print("    MORNING OFFICE AUTOMATION TOOL")
    print("    Streamline Your Daily Routine")
    print("🌅" + "=" * 60 + "🌅")
    print()

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    # Check Python
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 7:
        print(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"⚠️  Python version {python_version.major}.{python_version.minor} (3.7+ recommended)")
    
    # Check if we can import required modules
    try:
        import json
        import webbrowser
        import subprocess
        print("✓ Core Python modules available")
    except ImportError as e:
        print(f"✗ Missing core module: {e}")
        return False
    
    return True

def run_simple_mode():
    """Run the automation in simple mode (no external dependencies)"""
    print("🚀 Starting in Simple Mode...")
    print("   - Basic HTTP API server")
    print("   - Core automation features")
    print("   - No external dependencies required")
    print()
    
    try:
        # Run the simple backend
        backend_script = Path(__file__).parent / "backend" / "main_simple.py"
        subprocess.run([sys.executable, str(backend_script)])
    except KeyboardInterrupt:
        print("\n🛑 Automation stopped by user")
    except Exception as e:
        print(f"❌ Error running simple mode: {e}")

def run_full_mode():
    """Run the automation in full mode (with all features)"""
    print("🚀 Starting in Full Mode...")
    print("   - FastAPI backend with WebSocket support")
    print("   - React frontend with modern UI")
    print("   - Advanced window tracking")
    print()
    
    try:
        # Check if FastAPI is available
        import fastapi
        import uvicorn
        
        # Start backend
        backend_script = Path(__file__).parent / "backend" / "main.py"
        backend_process = subprocess.Popen([sys.executable, str(backend_script)])
        
        print("⏳ Starting backend server...")
        time.sleep(3)
        
        # Start frontend (if available)
        frontend_dir = Path(__file__).parent / "frontend"
        if frontend_dir.exists() and (frontend_dir / "package.json").exists():
            print("⏳ Starting frontend...")
            frontend_process = subprocess.Popen(
                ["npm", "start"], 
                cwd=frontend_dir,
                shell=True
            )
            
            time.sleep(5)
            print("🌐 Opening web interface...")
            webbrowser.open("http://localhost:3000")
            
            # Wait for processes
            try:
                backend_process.wait()
                frontend_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping services...")
                backend_process.terminate()
                frontend_process.terminate()
        else:
            print("⚠️  Frontend not available, running backend only")
            print("🌐 API available at: http://localhost:8000")
            backend_process.wait()
            
    except ImportError:
        print("⚠️  FastAPI not available, falling back to simple mode")
        run_simple_mode()
    except Exception as e:
        print(f"❌ Error running full mode: {e}")

def quick_launch():
    """Quick launch without starting servers"""
    print("⚡ Quick Launch Mode")
    print("   - Launch URLs and applications immediately")
    print("   - No server required")
    print()

    try:
        import json
        import webbrowser
        import subprocess
        import platform
        import time
        import uuid
        from datetime import datetime

        # Load configuration directly
        config_file = Path(__file__).parent / "config" / "settings.json"
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except:
            config = {
                "urls": [
                    {"id": "1", "name": "Gmail", "url": "https://mail.google.com", "category": "Email", "enabled": True},
                    {"id": "2", "name": "GitHub", "url": "https://github.com", "category": "Development", "enabled": True},
                    {"id": "3", "name": "Slack", "url": "https://slack.com", "category": "Communication", "enabled": True}
                ],
                "applications": [
                    {"id": "1", "name": "VS Code", "path": "code", "category": "Development", "enabled": True},
                    {"id": "2", "name": "Terminal", "path": "wt", "category": "Development", "enabled": True}
                ]
            }

        urls = [url for url in config.get('urls', []) if url.get('enabled', True)]
        applications = [app for app in config.get('applications', []) if app.get('enabled', True)]

        session_id = str(uuid.uuid4())

        # Launch URLs
        for url_config in urls:
            url = url_config.get('url')
            name = url_config.get('name', 'Unknown')
            print(f"🌐 Opening {name}: {url}")
            webbrowser.open(url)
            time.sleep(2)  # Delay between launches

        # Launch applications
        for app_config in applications:
            path = app_config.get('path')
            name = app_config.get('name', 'Unknown')
            print(f"🚀 Launching {name}: {path}")
            try:
                if platform.system() == "Windows":
                    subprocess.Popen(path, shell=True)
                else:
                    subprocess.Popen([path])
                time.sleep(2)  # Delay between launches
            except Exception as e:
                print(f"⚠️  Error launching {name}: {e}")

        print()
        print("🎉 Launch completed!")
        print(f"   - URLs opened: {len(urls)}")
        print(f"   - Apps launched: {len(applications)}")
        print(f"   - Session ID: {session_id}")
        print(f"   - Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except Exception as e:
        print(f"❌ Error in quick launch: {e}")

def run_ui_mode():
    """Run the desktop UI application"""
    print("🖥️  Starting Desktop UI...")
    print("   - Modern tkinter interface")
    print("   - Single-click automation")
    print("   - Real-time status updates")
    print()

    try:
        ui_script = Path(__file__).parent / "ui_app.py"
        subprocess.run([sys.executable, str(ui_script)])
    except Exception as e:
        print(f"❌ Error running UI mode: {e}")

def show_menu():
    """Show the main menu"""
    print("📋 Choose your automation mode:")
    print()
    print("1. 🖥️  Desktop UI     - Modern graphical interface (RECOMMENDED)")
    print("2. 🚀 Simple Mode    - Basic automation with HTTP API")
    print("3. 🌟 Full Mode      - Complete automation with modern web UI")
    print("4. ⚡ Quick Launch   - Launch routine immediately")
    print("5. 🧪 Test Backend   - Run backend tests")
    print("6. ❓ Help          - Show usage information")
    print("7. 🚪 Exit          - Quit the application")
    print()

def show_help():
    """Show help information"""
    print("📖 HELP - Morning Office Automation Tool")
    print()
    print("MODES:")
    print("  Simple Mode  - Runs a basic HTTP server for automation")
    print("  Full Mode    - Runs the complete application with UI")
    print("  Quick Launch - Immediately opens your configured URLs/apps")
    print()
    print("CONFIGURATION:")
    print("  Edit config/settings.json to customize your URLs and applications")
    print()
    print("API ENDPOINTS (when server is running):")
    print("  GET  /api/config - Get current configuration")
    print("  GET  /api/urls   - Get configured URLs")
    print("  POST /api/launch - Launch morning routine")
    print()
    print("EXAMPLES:")
    print("  python start_automation.py")
    print("  curl -X POST http://localhost:8000/api/launch")
    print()

def run_tests():
    """Run the backend tests"""
    print("🧪 Running Backend Tests...")
    print()
    
    try:
        test_script = Path(__file__).parent / "test_backend.py"
        subprocess.run([sys.executable, str(test_script)])
    except Exception as e:
        print(f"❌ Error running tests: {e}")

def main():
    """Main application entry point"""
    print_banner()
    
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages.")
        return
    
    print()
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode in ['ui', 'u', 'desktop']:
            run_ui_mode()
        elif mode in ['simple', 's']:
            run_simple_mode()
        elif mode in ['full', 'f']:
            run_full_mode()
        elif mode in ['quick', 'q']:
            quick_launch()
        elif mode in ['test', 't']:
            run_tests()
        elif mode in ['help', 'h']:
            show_help()
        else:
            print(f"❓ Unknown mode: {mode}")
            show_help()
        return
    
    # Interactive mode
    while True:
        show_menu()
        try:
            choice = input("👉 Enter your choice (1-7): ").strip()
            print()

            if choice == '1':
                run_ui_mode()
                break
            elif choice == '2':
                run_simple_mode()
                break
            elif choice == '3':
                run_full_mode()
                break
            elif choice == '4':
                quick_launch()
                break
            elif choice == '5':
                run_tests()
                input("\nPress Enter to continue...")
                print()
            elif choice == '6':
                show_help()
                input("\nPress Enter to continue...")
                print()
            elif choice == '7':
                print("👋 Goodbye! Have a productive day!")
                break
            else:
                print("❓ Invalid choice. Please enter 1-7.")
                print()
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Have a productive day!")
            break
        except EOFError:
            print("\n\n👋 Goodbye! Have a productive day!")
            break

if __name__ == "__main__":
    main()
