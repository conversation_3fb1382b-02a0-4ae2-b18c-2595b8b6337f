{"ast": null, "code": "/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 9V7a2 2 0 0 0-2-2h-6\",\n  key: \"19z8uc\"\n}], [\"path\", {\n  d: \"m15 2-3 3 3 3\",\n  key: \"177bxs\"\n}], [\"path\", {\n  d: \"M20 13v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2\",\n  key: \"d36hnl\"\n}]];\nconst RotateCcwSquare = createLucideIcon(\"rotate-ccw-square\", __iconNode);\nexport { __iconNode, RotateCcwSquare as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "RotateCcwSquare", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\node_modules\\lucide-react\\src\\icons\\rotate-ccw-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M20 9V7a2 2 0 0 0-2-2h-6', key: '19z8uc' }],\n  ['path', { d: 'm15 2-3 3 3 3', key: '177bxs' }],\n  ['path', { d: 'M20 13v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2', key: 'd36hnl' }],\n];\n\n/**\n * @component @name RotateCcwSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgOVY3YTIgMiAwIDAgMC0yLTJoLTYiIC8+CiAgPHBhdGggZD0ibTE1IDItMyAzIDMgMyIgLz4KICA8cGF0aCBkPSJNMjAgMTN2NWEyIDIgMCAwIDEtMiAySDZhMiAyIDAgMCAxLTItMlY3YTIgMiAwIDAgMSAyLTJoMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/rotate-ccw-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCcwSquare = createLucideIcon('rotate-ccw-square', __iconNode);\n\nexport default RotateCcwSquare;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAU,GAC3F;AAaM,MAAAC,eAAA,GAAkBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}