@echo off
title 🚀 Daily Work Launcher - Setup
color 0B

echo.
echo ================================================
echo    🚀 DAILY WORK LAUNCHER - QUICK SETUP
echo    Professional Window Management Tool
echo ================================================
echo.

echo 📋 Installing required packages...
python -m pip install --upgrade pip
python -m pip install psutil requests Pillow

echo.
echo ✅ Setup complete! Starting Daily Work Launcher...
echo.

python daily_launcher.py

pause
