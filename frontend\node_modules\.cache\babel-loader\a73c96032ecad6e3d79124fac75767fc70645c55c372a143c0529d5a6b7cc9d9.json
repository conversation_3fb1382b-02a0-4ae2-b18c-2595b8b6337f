{"ast": null, "code": "import platform from './node/index.js';\nimport * as utils from './common/utils.js';\nexport default {\n  ...utils,\n  ...platform\n};", "map": {"version": 3, "names": ["platform", "utils"], "sources": ["C:/Users/<USER>/Desktop/acf-automatcion/frontend/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\nimport * as utils from './common/utils.js';\n\nexport default {\n  ...utils,\n  ...platform\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAE1C,eAAe;EACb,GAAGA,KAAK;EACR,GAAGD;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}