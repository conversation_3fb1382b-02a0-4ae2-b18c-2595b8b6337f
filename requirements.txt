"""
🚀 Daily Work Launcher Requirements
Install these packages for full functionality
"""

# Core packages (required)
tkinter  # Usually comes with Python
webbrowser  # Built-in module
subprocess  # Built-in module
threading  # Built-in module
json  # Built-in module
os  # Built-in module
datetime  # Built-in module
urllib  # Built-in module

# Optional packages for enhanced features
psutil>=5.9.0  # For advanced process monitoring
requests>=2.31.0  # For web-based features
Pillow>=10.0.0  # For enhanced UI icons (optional)

# Development dependencies
pytest>=7.4.0  # For testing (optional)
black>=23.0.0  # For code formatting (optional)
