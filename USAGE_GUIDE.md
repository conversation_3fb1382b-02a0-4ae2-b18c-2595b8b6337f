# 🌅 Morning Office Automation - Usage Guide

## 🚀 Quick Start

### For Windows Users (Easiest):
1. **Double-click `Morning_Automation.bat`**
2. The desktop UI will open automatically
3. Configure your URLs and applications
4. Click "Start Morning Routine" or "Quick Launch"

### For All Platforms:
```bash
# Start the desktop UI
python ui_app.py

# Or use the interactive menu
python start_automation.py
```

## 🖥️ Desktop UI Features

### Main Interface:
- **🚀 Start Morning Routine**: Full automation with session tracking
- **⚡ Quick Launch**: Instant launch without session tracking
- **📊 Dashboard**: Overview of configured items and session status
- **🌐 URLs Tab**: Manage website URLs with checkboxes
- **🚀 Applications Tab**: Manage applications to launch
- **⏱️ Timing Tab**: Session tracking and activity log

### Quick Actions (Dashboard):
- **📧 Open Email**: Launch only email-related URLs/apps
- **💻 Dev Tools**: Launch development tools and websites
- **💬 Communication**: Launch chat and communication apps

## ⚡ Command Line Options

```bash
# Desktop UI (recommended)
python start_automation.py ui
python ui_app.py

# Quick launch (no UI)
python start_automation.py quick

# Interactive menu
python start_automation.py

# API server mode
python start_automation.py simple

# Run tests
python start_automation.py test
```

## 🔧 Configuration

### Default URLs:
- Gmail (https://mail.google.com)
- GitHub (https://github.com)
- Slack (https://slack.com)
- LinkedIn (https://linkedin.com)
- Google Calendar (https://calendar.google.com)

### Default Applications:
- VS Code (`code`)
- Windows Terminal (`wt`)
- Notepad (`notepad`)

### Customizing:
1. **Via UI**: Use checkboxes in URLs/Applications tabs
2. **Via File**: Edit `config/settings.json`

## 📁 File Structure

```
acf-automation/
├── ui_app.py                 # 🖥️ Desktop UI (main application)
├── start_automation.py       # 🚀 Startup script with options
├── Morning_Automation.bat    # 🪟 Windows batch file
├── test_backend.py          # 🧪 Test suite
├── config/settings.json     # ⚙️ Configuration file
├── data/timing_logs.json    # 📊 Session data
└── backend/                 # 🔧 Backend services
```

## 🎯 Use Cases

### Daily Morning Routine:
1. Arrive at office
2. Double-click `Morning_Automation.bat`
3. Click "Start Morning Routine"
4. All your work tools open automatically

### Category-Specific Launch:
- Need just email? Click "📧 Open Email"
- Starting development? Click "💻 Dev Tools"
- Time for meetings? Click "💬 Communication"

### Quick Testing:
- Use "⚡ Quick Launch" for instant opening
- No session tracking, just fast automation

## 🛠️ Troubleshooting

### UI Won't Open:
```bash
# Try direct launch
python ui_app.py

# Check Python installation
python --version
```

### Applications Won't Launch:
- Check application paths in configuration
- Ensure applications are installed
- Try running as administrator (Windows)

### Configuration Issues:
```bash
# Reset to defaults
python test_backend.py
```

## 🎨 UI Features

### Real-time Updates:
- ✅ Status changes instantly
- 📝 Activity log shows all actions
- ⏱️ Session timer updates live
- 🔄 Configuration saves automatically

### Visual Feedback:
- 🟢 Green badges for enabled items
- 📊 Statistics cards on dashboard
- 📋 Scrollable activity log
- 🎯 Category-based organization

## 🚀 Advanced Features

### Session Tracking:
- Unique session IDs
- Start/end timestamps
- Activity logging
- Duration tracking

### Category System:
- **Email**: Mail applications
- **Development**: Code editors, terminals, GitHub
- **Communication**: Chat, video calls
- **Productivity**: Calendar, notes
- **Social**: LinkedIn, Twitter

### Multi-mode Operation:
- **Desktop UI**: Full graphical interface
- **Quick Launch**: Command-line instant launch
- **API Server**: HTTP endpoints for automation
- **Test Mode**: Verify functionality

## 📈 Benefits

### Time Savings:
- ⏱️ Save 5-10 minutes every morning
- 🔄 Consistent routine every day
- 🎯 No forgotten applications or websites

### Productivity:
- 🚀 Instant work environment setup
- 📊 Track your automation usage
- 🎨 Customize for your workflow

### Reliability:
- ✅ Never forget to open important tools
- 🔧 Easy to modify and update
- 🌐 Works across different platforms

## 🎉 Success Tips

1. **Start Simple**: Enable just a few essential URLs/apps
2. **Customize Gradually**: Add more items as you get comfortable
3. **Use Categories**: Organize by work type for targeted launches
4. **Check Logs**: Use the Timing tab to see what's happening
5. **Test First**: Use Quick Launch to verify everything works

---

**Enjoy your streamlined morning routine! ☕🚀**
