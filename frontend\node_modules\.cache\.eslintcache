[{"C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\hooks\\useWebSocket.ts": "4", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\hooks\\useAPI.ts": "5", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\TimingDisplay.tsx": "7", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\URLManager.tsx": "8", "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\SettingsPanel.tsx": "9"}, {"size": 554, "mtime": 1754062462503, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1754062461353, "results": "12", "hashOfConfig": "11"}, {"size": 4329, "mtime": 1754062630454, "results": "13", "hashOfConfig": "11"}, {"size": 2332, "mtime": 1754062678013, "results": "14", "hashOfConfig": "11"}, {"size": 3488, "mtime": 1754062662865, "results": "15", "hashOfConfig": "11"}, {"size": 7721, "mtime": 1754062712457, "results": "16", "hashOfConfig": "11"}, {"size": 8980, "mtime": 1754062786741, "results": "17", "hashOfConfig": "11"}, {"size": 8250, "mtime": 1754062746781, "results": "18", "hashOfConfig": "11"}, {"size": 10733, "mtime": 1754062826050, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "y8ro6e", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\App.tsx", ["47"], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\hooks\\useWebSocket.ts", ["48", "49"], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\hooks\\useAPI.ts", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\TimingDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\URLManager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\src\\components\\SettingsPanel.tsx", ["50"], [], {"ruleId": "51", "severity": 1, "message": "52", "line": 1, "column": 27, "nodeType": "53", "messageId": "54", "endLine": 1, "endColumn": 36}, {"ruleId": "51", "severity": 1, "message": "55", "line": 2, "column": 10, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 12}, {"ruleId": "51", "severity": 1, "message": "56", "line": 2, "column": 14, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 20}, {"ruleId": "57", "severity": 1, "message": "58", "line": 31, "column": 6, "nodeType": "59", "endLine": 31, "endColumn": 14, "suggestions": "60"}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'io' is defined but never used.", "'Socket' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'settings.preferences' and 'settings.timing'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSettings' needs the current value of 'settings.preferences'.", "ArrayExpression", ["61"], {"desc": "62", "fix": "63"}, "Update the dependencies array to be: [config, settings.preferences, settings.timing]", {"range": "64", "text": "65"}, [868, 876], "[config, settings.preferences, settings.timing]"]