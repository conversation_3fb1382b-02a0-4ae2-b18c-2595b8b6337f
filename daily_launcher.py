#!/usr/bin/env python3
"""
🚀 Daily Work Launcher - Professional Window Management Tool
Opens all your daily work windows with timing tracking and modern UI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import webbrowser
import subprocess
import threading
import time
import json
import os
from datetime import datetime, timedelta
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

from urllib.parse import urlparse

class WorkspaceLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Daily Work Launcher - Professional Edition")
        self.root.geometry("900x700")
        self.root.configure(bg='#1e1e1e')
        
        # Configure style
        self.setup_styles()
        
        # Data storage
        self.config_file = "workspace_config.json"
        self.windows_data = []
        self.timing_data = []
        self.is_tracking = False
        
        # Load saved configuration
        self.load_config()
        
        # Create UI
        self.create_ui()
        
        # Start timing thread
        self.start_timing_thread()
    
    def setup_styles(self):
        """Setup modern dark theme styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors for dark theme
        style.configure('Title.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ffffff', 
                       font=('Segoe UI', 16, 'bold'))
        
        style.configure('Heading.TLabel', 
                       background='#1e1e1e', 
                       foreground='#00d4aa', 
                       font=('Segoe UI', 12, 'bold'))
        
        style.configure('Info.TLabel', 
                       background='#1e1e1e', 
                       foreground='#cccccc', 
                       font=('Segoe UI', 10))
        
        style.configure('Custom.TButton',
                       background='#0078d4',
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Success.TButton',
                       background='#107c10',
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Danger.TButton',
                       background='#d13438',
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
    
    def create_ui(self):
        """Create the main user interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, 
                               text="🚀 Daily Work Launcher", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # Tab 1: Window Configuration
        self.create_config_tab()
        
        # Tab 2: Launch Control
        self.create_launch_tab()
        
        # Tab 3: Timing Dashboard
        self.create_timing_tab()
        
        # Tab 4: Settings
        self.create_settings_tab()
    
    def create_config_tab(self):
        """Create window configuration tab"""
        config_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(config_frame, text="⚙️ Configure Windows")
        
        # Header
        header_label = ttk.Label(config_frame, 
                                text="Configure Your Daily Windows", 
                                style='Heading.TLabel')
        header_label.pack(pady=10)
        
        # Input frame
        input_frame = tk.Frame(config_frame, bg='#2d2d2d')
        input_frame.pack(fill='x', padx=20, pady=10)
        
        # URL/Application input
        tk.Label(input_frame, text="URL or Application Path:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.url_entry = tk.Entry(input_frame, width=60, font=('Segoe UI', 10))
        self.url_entry.pack(fill='x', pady=5)
        
        # Name input
        tk.Label(input_frame, text="Window Name:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.name_entry = tk.Entry(input_frame, width=60, font=('Segoe UI', 10))
        self.name_entry.pack(fill='x', pady=5)
        
        # Delay input
        tk.Label(input_frame, text="Launch Delay (seconds):", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.delay_entry = tk.Entry(input_frame, width=10, font=('Segoe UI', 10))
        self.delay_entry.pack(anchor='w', pady=5)
        self.delay_entry.insert(0, "2")
        
        # Buttons
        button_frame = tk.Frame(input_frame, bg='#2d2d2d')
        button_frame.pack(fill='x', pady=10)
        
        ttk.Button(button_frame, text="➕ Add Window", 
                  command=self.add_window, style='Success.TButton').pack(side='left', padx=5)
        ttk.Button(button_frame, text="📁 Browse App", 
                  command=self.browse_application, style='Custom.TButton').pack(side='left', padx=5)
        ttk.Button(button_frame, text="🌐 Add Common Sites", 
                  command=self.add_common_sites, style='Custom.TButton').pack(side='left', padx=5)
        
        # Windows list
        list_frame = tk.Frame(config_frame, bg='#2d2d2d')
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(list_frame, text="Configured Windows:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 12, 'bold')).pack(anchor='w')
        
        # Treeview for windows list
        columns = ('Name', 'URL/Path', 'Delay', 'Status')
        self.windows_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.windows_tree.heading(col, text=col)
            if col == 'Name':
                self.windows_tree.column(col, width=150)
            elif col == 'URL/Path':
                self.windows_tree.column(col, width=300)
            elif col == 'Delay':
                self.windows_tree.column(col, width=80)
            else:
                self.windows_tree.column(col, width=100)
        
        self.windows_tree.pack(fill='both', expand=True)
        
        # List buttons
        list_buttons = tk.Frame(list_frame, bg='#2d2d2d')
        list_buttons.pack(fill='x', pady=5)
        
        ttk.Button(list_buttons, text="🗑️ Remove Selected", 
                  command=self.remove_window, style='Danger.TButton').pack(side='left', padx=5)
        ttk.Button(list_buttons, text="✏️ Edit Selected", 
                  command=self.edit_window, style='Custom.TButton').pack(side='left', padx=5)
        ttk.Button(list_buttons, text="💾 Save Config", 
                  command=self.save_config, style='Success.TButton').pack(side='right', padx=5)
        
        self.refresh_windows_list()
    
    def create_launch_tab(self):
        """Create launch control tab"""
        launch_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(launch_frame, text="🚀 Launch Control")
        
        # Header
        header_label = ttk.Label(launch_frame, 
                                text="Launch Your Daily Workspace", 
                                style='Heading.TLabel')
        header_label.pack(pady=20)
        
        # Status display
        self.status_frame = tk.Frame(launch_frame, bg='#2d2d2d')
        self.status_frame.pack(fill='x', padx=20, pady=10)
        
        self.status_label = tk.Label(self.status_frame, 
                                    text="Ready to launch windows", 
                                    bg='#2d2d2d', fg='#00d4aa', 
                                    font=('Segoe UI', 12, 'bold'))
        self.status_label.pack()
        
        # Progress bar
        self.progress = ttk.Progressbar(launch_frame, length=400, mode='determinate')
        self.progress.pack(pady=10)
        
        # Launch buttons
        button_frame = tk.Frame(launch_frame, bg='#2d2d2d')
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="🚀 Launch All Windows", 
                  command=self.launch_all_windows, 
                  style='Success.TButton').pack(side='left', padx=10)
        
        ttk.Button(button_frame, text="⏹️ Stop Launching", 
                  command=self.stop_launching, 
                  style='Danger.TButton').pack(side='left', padx=10)
        
        ttk.Button(button_frame, text="🔄 Test Single Window", 
                  command=self.test_single_window, 
                  style='Custom.TButton').pack(side='left', padx=10)
        
        # Launch log
        log_frame = tk.Frame(launch_frame, bg='#2d2d2d')
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(log_frame, text="Launch Log:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 12, 'bold')).pack(anchor='w')
        
        # Create scrolled text widget
        self.log_text = tk.Text(log_frame, height=15, width=80, 
                               bg='#1e1e1e', fg='#ffffff', 
                               font=('Consolas', 9))
        scrollbar = tk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_timing_tab(self):
        """Create timing dashboard tab"""
        timing_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(timing_frame, text="⏱️ Timing Dashboard")
        
        # Header
        header_label = ttk.Label(timing_frame, 
                                text="Window Usage Analytics", 
                                style='Heading.TLabel')
        header_label.pack(pady=10)
        
        # Current session info
        session_frame = tk.Frame(timing_frame, bg='#2d2d2d')
        session_frame.pack(fill='x', padx=20, pady=10)
        
        self.session_label = tk.Label(session_frame, 
                                     text="Session Started: Not Started", 
                                     bg='#2d2d2d', fg='#ffffff', 
                                     font=('Segoe UI', 10))
        self.session_label.pack(anchor='w')
        
        self.total_time_label = tk.Label(session_frame, 
                                        text="Total Active Time: 00:00:00", 
                                        bg='#2d2d2d', fg='#00d4aa', 
                                        font=('Segoe UI', 12, 'bold'))
        self.total_time_label.pack(anchor='w')
        
        # Timing table
        timing_table_frame = tk.Frame(timing_frame, bg='#2d2d2d')
        timing_table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(timing_table_frame, text="Window Timing Details:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 12, 'bold')).pack(anchor='w')
        
        # Timing treeview
        timing_columns = ('Window', 'Opened', 'Duration', 'Status')
        self.timing_tree = ttk.Treeview(timing_table_frame, columns=timing_columns, 
                                       show='headings', height=15)
        
        for col in timing_columns:
            self.timing_tree.heading(col, text=col)
            if col == 'Window':
                self.timing_tree.column(col, width=200)
            elif col == 'Opened':
                self.timing_tree.column(col, width=150)
            elif col == 'Duration':
                self.timing_tree.column(col, width=100)
            else:
                self.timing_tree.column(col, width=100)
        
        self.timing_tree.pack(fill='both', expand=True)
        
        # Control buttons
        timing_buttons = tk.Frame(timing_table_frame, bg='#2d2d2d')
        timing_buttons.pack(fill='x', pady=5)
        
        ttk.Button(timing_buttons, text="🔄 Refresh", 
                  command=self.refresh_timing, style='Custom.TButton').pack(side='left', padx=5)
        ttk.Button(timing_buttons, text="📊 Export Report", 
                  command=self.export_timing_report, style='Custom.TButton').pack(side='left', padx=5)
        ttk.Button(timing_buttons, text="🗑️ Clear History", 
                  command=self.clear_timing_history, style='Danger.TButton').pack(side='right', padx=5)
    
    def create_settings_tab(self):
        """Create settings tab"""
        settings_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(settings_frame, text="⚙️ Settings")
        
        # Header
        header_label = ttk.Label(settings_frame, 
                                text="Application Settings", 
                                style='Heading.TLabel')
        header_label.pack(pady=10)
        
        # Settings options
        options_frame = tk.Frame(settings_frame, bg='#2d2d2d')
        options_frame.pack(fill='x', padx=20, pady=10)
        
        # Auto-start option
        self.auto_start_var = tk.BooleanVar()
        tk.Checkbutton(options_frame, text="Auto-start with Windows", 
                      variable=self.auto_start_var, 
                      bg='#2d2d2d', fg='#ffffff', 
                      selectcolor='#2d2d2d', 
                      font=('Segoe UI', 10)).pack(anchor='w', pady=5)
        
        # Minimize to tray option
        self.minimize_tray_var = tk.BooleanVar()
        tk.Checkbutton(options_frame, text="Minimize to system tray", 
                      variable=self.minimize_tray_var, 
                      bg='#2d2d2d', fg='#ffffff', 
                      selectcolor='#2d2d2d', 
                      font=('Segoe UI', 10)).pack(anchor='w', pady=5)
        
        # Launch delay setting
        delay_frame = tk.Frame(options_frame, bg='#2d2d2d')
        delay_frame.pack(fill='x', pady=10)
        
        tk.Label(delay_frame, text="Default launch delay (seconds):", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.default_delay_entry = tk.Entry(delay_frame, width=10, font=('Segoe UI', 10))
        self.default_delay_entry.pack(anchor='w', pady=5)
        self.default_delay_entry.insert(0, "2")
        
        # Browser selection
        browser_frame = tk.Frame(options_frame, bg='#2d2d2d')
        browser_frame.pack(fill='x', pady=10)
        
        tk.Label(browser_frame, text="Default Browser:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 10)).pack(anchor='w')
        self.browser_var = tk.StringVar(value="default")
        browsers = [("System Default", "default"), ("Chrome", "chrome"), ("Firefox", "firefox"), ("Edge", "edge")]
        
        for text, value in browsers:
            tk.Radiobutton(browser_frame, text=text, variable=self.browser_var, value=value,
                          bg='#2d2d2d', fg='#ffffff', selectcolor='#2d2d2d',
                          font=('Segoe UI', 10)).pack(anchor='w')
        
        # Save settings button
        ttk.Button(options_frame, text="💾 Save Settings", 
                  command=self.save_settings, style='Success.TButton').pack(pady=20)
        
        # About section
        about_frame = tk.Frame(settings_frame, bg='#2d2d2d')
        about_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(about_frame, text="About Daily Work Launcher", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 12, 'bold')).pack(anchor='w')
        
        about_text = """
🚀 Daily Work Launcher v1.0
Professional window management tool for productivity

Features:
• Launch multiple websites and applications
• Track window usage timing
• Modern dark theme interface
• Export timing reports
• Customizable launch delays
• Browser selection options

Created with Python & Tkinter
        """
        
        tk.Label(about_frame, text=about_text, 
                bg='#2d2d2d', fg='#cccccc', 
                font=('Segoe UI', 9), justify='left').pack(anchor='w')
    
    def add_window(self):
        """Add a new window to the configuration"""
        url = self.url_entry.get().strip()
        name = self.name_entry.get().strip()
        delay = self.delay_entry.get().strip()
        
        if not url or not name:
            messagebox.showerror("Error", "Please enter both URL/Path and Name")
            return
        
        try:
            delay = float(delay) if delay else 2.0
        except ValueError:
            delay = 2.0
        
        window_config = {
            'name': name,
            'url': url,
            'delay': delay,
            'type': 'url' if url.startswith(('http://', 'https://')) else 'app'
        }
        
        self.windows_data.append(window_config)
        self.refresh_windows_list()
        
        # Clear inputs
        self.url_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.delay_entry.delete(0, tk.END)
        self.delay_entry.insert(0, "2")
        
        self.log_message(f"✅ Added window: {name}")
    
    def browse_application(self):
        """Browse for an application executable"""
        file_path = filedialog.askopenfilename(
            title="Select Application",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, file_path)
    
    def add_common_sites(self):
        """Add common work sites quickly"""
        common_sites = [
            ("Gmail", "https://mail.google.com"),
            ("Outlook", "https://outlook.live.com"),
            ("Slack", "https://slack.com"),
            ("Teams", "https://teams.microsoft.com"),
            ("GitHub", "https://github.com"),
            ("LinkedIn", "https://linkedin.com"),
            ("WhatsApp Web", "https://web.whatsapp.com"),
            ("Google Drive", "https://drive.google.com"),
            ("Notion", "https://notion.so"),
            ("Trello", "https://trello.com")
        ]
        
        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Common Sites")
        dialog.geometry("400x500")
        dialog.configure(bg='#2d2d2d')
        dialog.grab_set()
        
        tk.Label(dialog, text="Select sites to add:", 
                bg='#2d2d2d', fg='#ffffff', font=('Segoe UI', 12, 'bold')).pack(pady=10)
        
        # Create checkboxes for each site
        site_vars = {}
        for name, url in common_sites:
            var = tk.BooleanVar()
            site_vars[name] = (var, url)
            tk.Checkbutton(dialog, text=f"{name} ({url})", variable=var,
                          bg='#2d2d2d', fg='#ffffff', selectcolor='#2d2d2d',
                          font=('Segoe UI', 9)).pack(anchor='w', padx=20, pady=2)
        
        def add_selected():
            for name, (var, url) in site_vars.items():
                if var.get():
                    window_config = {
                        'name': name,
                        'url': url,
                        'delay': 2.0,
                        'type': 'url'
                    }
                    self.windows_data.append(window_config)
            
            self.refresh_windows_list()
            dialog.destroy()
            self.log_message(f"✅ Added selected common sites")
        
        button_frame = tk.Frame(dialog, bg='#2d2d2d')
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="Add Selected", command=add_selected).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side='left', padx=5)
    
    def remove_window(self):
        """Remove selected window from configuration"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a window to remove")
            return
        
        item = self.windows_tree.item(selection[0])
        window_name = item['values'][0]
        
        # Find and remove the window
        self.windows_data = [w for w in self.windows_data if w['name'] != window_name]
        self.refresh_windows_list()
        self.log_message(f"🗑️ Removed window: {window_name}")
    
    def edit_window(self):
        """Edit selected window configuration"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a window to edit")
            return
        
        item = self.windows_tree.item(selection[0])
        window_name = item['values'][0]
        
        # Find the window data
        window_data = next((w for w in self.windows_data if w['name'] == window_name), None)
        if not window_data:
            return
        
        # Fill the input fields
        self.url_entry.delete(0, tk.END)
        self.url_entry.insert(0, window_data['url'])
        self.name_entry.delete(0, tk.END)
        self.name_entry.insert(0, window_data['name'])
        self.delay_entry.delete(0, tk.END)
        self.delay_entry.insert(0, str(window_data['delay']))
        
        # Remove the old entry
        self.windows_data = [w for w in self.windows_data if w['name'] != window_name]
        self.refresh_windows_list()
    
    def refresh_windows_list(self):
        """Refresh the windows list display"""
        # Clear existing items
        for item in self.windows_tree.get_children():
            self.windows_tree.delete(item)
        
        # Add current windows
        for window in self.windows_data:
            self.windows_tree.insert('', 'end', values=(
                window['name'],
                window['url'],
                f"{window['delay']}s",
                "Ready"
            ))
    
    def launch_all_windows(self):
        """Launch all configured windows"""
        if not self.windows_data:
            messagebox.showwarning("Warning", "No windows configured. Please add some windows first.")
            return
        
        self.log_message("🚀 Starting window launch sequence...")
        self.progress['maximum'] = len(self.windows_data)
        self.progress['value'] = 0
        
        # Start launch in separate thread
        threading.Thread(target=self._launch_windows_thread, daemon=True).start()
    
    def _launch_windows_thread(self):
        """Launch windows in background thread"""
        start_time = datetime.now()
        self.is_tracking = True
        
        for i, window in enumerate(self.windows_data):
            try:
                self.root.after(0, lambda w=window: self.status_label.config(text=f"Launching: {w['name']}"))
                
                launch_time = datetime.now()
                
                if window['type'] == 'url':
                    self._launch_url(window['url'])
                else:
                    self._launch_application(window['url'])
                
                # Add to timing data
                timing_entry = {
                    'name': window['name'],
                    'opened': launch_time.strftime("%H:%M:%S"),
                    'start_time': launch_time,
                    'status': 'Active'
                }
                self.timing_data.append(timing_entry)
                
                self.log_message(f"✅ Launched: {window['name']}")
                
                # Update progress
                self.root.after(0, lambda: self.progress.config(value=i+1))
                
                # Wait before next launch
                time.sleep(window['delay'])
                
            except Exception as e:
                self.log_message(f"❌ Failed to launch {window['name']}: {str(e)}")
        
        total_time = datetime.now() - start_time
        self.root.after(0, lambda: self.status_label.config(text=f"✅ All windows launched in {total_time.seconds}s"))
        self.root.after(0, lambda: self.session_label.config(text=f"Session Started: {start_time.strftime('%H:%M:%S')}"))
        self.log_message(f"🎉 Launch sequence completed in {total_time.seconds} seconds")
    
    def _launch_url(self, url):
        """Launch a URL in the default browser"""
        browser_type = getattr(self, 'browser_var', tk.StringVar(value="default")).get()
        
        if browser_type == "chrome":
            try:
                subprocess.Popen([
                    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                    "--new-window", url
                ])
                return
            except FileNotFoundError:
                pass
        elif browser_type == "firefox":
            try:
                subprocess.Popen(["firefox", "-new-window", url])
                return
            except FileNotFoundError:
                pass
        elif browser_type == "edge":
            try:
                subprocess.Popen(["msedge", "--new-window", url])
                return
            except FileNotFoundError:
                pass
        
        # Fallback to default browser
        webbrowser.open(url)
    
    def _launch_application(self, app_path):
        """Launch an application"""
        subprocess.Popen([app_path])
    
    def stop_launching(self):
        """Stop the launch sequence"""
        self.is_tracking = False
        self.status_label.config(text="🛑 Launch sequence stopped")
        self.log_message("🛑 Launch sequence stopped by user")
    
    def test_single_window(self):
        """Test launching a single selected window"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a window to test")
            return
        
        item = self.windows_tree.item(selection[0])
        window_name = item['values'][0]
        
        # Find the window data
        window_data = next((w for w in self.windows_data if w['name'] == window_name), None)
        if not window_data:
            return
        
        try:
            if window_data['type'] == 'url':
                self._launch_url(window_data['url'])
            else:
                self._launch_application(window_data['url'])
            
            self.log_message(f"🧪 Test launched: {window_name}")
        except Exception as e:
            self.log_message(f"❌ Test failed for {window_name}: {str(e)}")
    
    def start_timing_thread(self):
        """Start the timing update thread"""
        def update_timing():
            while True:
                if self.is_tracking:
                    self.root.after(0, self.update_timing_display)
                time.sleep(1)
        
        threading.Thread(target=update_timing, daemon=True).start()
    
    def update_timing_display(self):
        """Update the timing display"""
        if not self.timing_data:
            return
        
        current_time = datetime.now()
        total_duration = timedelta()
        
        # Clear timing tree
        for item in self.timing_tree.get_children():
            self.timing_tree.delete(item)
        
        # Update timing data
        for timing in self.timing_data:
            if 'start_time' in timing:
                duration = current_time - timing['start_time']
                total_duration += duration
                
                self.timing_tree.insert('', 'end', values=(
                    timing['name'],
                    timing['opened'],
                    str(duration).split('.')[0],  # Remove microseconds
                    timing['status']
                ))
        
        # Update total time
        self.total_time_label.config(text=f"Total Active Time: {str(total_duration).split('.')[0]}")
    
    def refresh_timing(self):
        """Refresh timing display"""
        self.update_timing_display()
        self.log_message("🔄 Timing data refreshed")
    
    def export_timing_report(self):
        """Export timing report to file"""
        if not self.timing_data:
            messagebox.showinfo("Info", "No timing data to export")
            return
        
        filename = f"timing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialvalue=filename
        )
        
        if filepath:
            try:
                with open(filepath, 'w') as f:
                    f.write("Daily Work Launcher - Timing Report\n")
                    f.write("=" * 40 + "\n\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    for timing in self.timing_data:
                        f.write(f"Window: {timing['name']}\n")
                        f.write(f"Opened: {timing['opened']}\n")
                        if 'start_time' in timing:
                            duration = datetime.now() - timing['start_time']
                            f.write(f"Duration: {str(duration).split('.')[0]}\n")
                        f.write(f"Status: {timing['status']}\n")
                        f.write("-" * 30 + "\n")
                
                messagebox.showinfo("Success", f"Timing report exported to:\n{filepath}")
                self.log_message(f"📊 Timing report exported to {filepath}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")
    
    def clear_timing_history(self):
        """Clear timing history"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear all timing history?"):
            self.timing_data.clear()
            self.update_timing_display()
            self.total_time_label.config(text="Total Active Time: 00:00:00")
            self.session_label.config(text="Session Started: Not Started")
            self.log_message("🗑️ Timing history cleared")
    
    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def save_config(self):
        """Save configuration to file"""
        config = {
            'windows': self.windows_data,
            'settings': {
                'default_delay': getattr(self, 'default_delay_entry', None) and self.default_delay_entry.get() or "2",
                'browser': getattr(self, 'browser_var', tk.StringVar(value="default")).get(),
                'auto_start': getattr(self, 'auto_start_var', tk.BooleanVar()).get(),
                'minimize_tray': getattr(self, 'minimize_tray_var', tk.BooleanVar()).get()
            }
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            messagebox.showinfo("Success", "Configuration saved successfully!")
            self.log_message("💾 Configuration saved")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")
    
    def load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.windows_data = config.get('windows', [])
                settings = config.get('settings', {})
                
                # Apply settings after UI is created
                self.root.after(100, lambda: self._apply_loaded_settings(settings))
                
            except Exception as e:
                self.log_message(f"⚠️ Failed to load configuration: {str(e)}")
    
    def _apply_loaded_settings(self, settings):
        """Apply loaded settings to UI"""
        try:
            if hasattr(self, 'default_delay_entry'):
                self.default_delay_entry.delete(0, tk.END)
                self.default_delay_entry.insert(0, settings.get('default_delay', '2'))
            
            if hasattr(self, 'browser_var'):
                self.browser_var.set(settings.get('browser', 'default'))
            
            if hasattr(self, 'auto_start_var'):
                self.auto_start_var.set(settings.get('auto_start', False))
            
            if hasattr(self, 'minimize_tray_var'):
                self.minimize_tray_var.set(settings.get('minimize_tray', False))
        except Exception as e:
            self.log_message(f"⚠️ Error applying settings: {str(e)}")
    
    def save_settings(self):
        """Save application settings"""
        self.save_config()
    
    def run(self):
        """Run the application"""
        self.log_message("🚀 Daily Work Launcher started")
        self.log_message("💡 Add your daily windows in the Configure tab")
        self.log_message("🎯 Click 'Launch All Windows' when ready to start your day")
        
        self.root.mainloop()

if __name__ == "__main__":
    app = WorkspaceLauncher()
    app.run()
