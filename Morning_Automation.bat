@echo off
title Morning Office Automation
cd /d "%~dp0"

echo.
echo ========================================
echo   MORNING OFFICE AUTOMATION TOOL
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if py launcher is available (preferred)
py --version >nul 2>&1
if not errorlevel 1 (
    echo Starting Desktop UI...
    py ui_app.py
) else (
    echo Starting Desktop UI...
    python ui_app.py
)

if errorlevel 1 (
    echo.
    echo Error starting the application.
    echo Trying alternative startup method...
    echo.
    
    REM Fallback to startup script
    if exist start_automation.py (
        py start_automation.py ui 2>nul || python start_automation.py ui
    ) else (
        echo Error: Application files not found.
        echo Please ensure you're running this from the correct directory.
    )
)

echo.
echo Application closed.
pause
