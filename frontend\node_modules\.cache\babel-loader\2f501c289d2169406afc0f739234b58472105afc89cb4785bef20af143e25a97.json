{"ast": null, "code": "/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.971 4.285A2 2 0 0 1 17 6v12a2 2 0 0 1-3.029 1.715l-9.997-5.998a2 2 0 0 1-.003-3.432z\",\n  key: \"19qhus\"\n}], [\"path\", {\n  d: \"M21 20V4\",\n  key: \"cb8qj8\"\n}]];\nconst StepBack = createLucideIcon(\"step-back\", __iconNode);\nexport { __iconNode, StepBack as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "StepBack", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\node_modules\\lucide-react\\src\\icons\\step-back.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M13.971 4.285A2 2 0 0 1 17 6v12a2 2 0 0 1-3.029 1.715l-9.997-5.998a2 2 0 0 1-.003-3.432z',\n      key: '19qhus',\n    },\n  ],\n  ['path', { d: 'M21 20V4', key: 'cb8qj8' }],\n];\n\n/**\n * @component @name StepBack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuOTcxIDQuMjg1QTIgMiAwIDAgMSAxNyA2djEyYTIgMiAwIDAgMS0zLjAyOSAxLjcxNWwtOS45OTctNS45OThhMiAyIDAgMCAxLS4wMDMtMy40MzJ6IiAvPgogIDxwYXRoIGQ9Ik0yMSAyMFY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/step-back\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst StepBack = createLucideIcon('step-back', __iconNode);\n\nexport default StepBack;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}