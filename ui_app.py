#!/usr/bin/env python3
"""
Morning Office Automation - Desktop UI
Modern desktop interface using tkinter with custom styling
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import webbrowser
import subprocess
import platform
import time
import uuid
import threading
from datetime import datetime
from pathlib import Path
import sys
import os

class ModernAutomationUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.load_config()
        self.create_widgets()
        self.session_active = False
        self.session_start_time = None
        self.opened_windows = []
        
    def setup_window(self):
        """Setup the main window"""
        self.root.title("🌅 Morning Office Automation")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
        
        # Set icon and styling
        try:
            self.root.iconbitmap(default="")  # You can add an icon file here
        except:
            pass
            
        # Configure colors
        self.colors = {
            'bg': '#f8fafc',
            'primary': '#3b82f6',
            'primary_hover': '#2563eb',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'text': '#1f2937',
            'text_light': '#6b7280',
            'border': '#e5e7eb',
            'card': '#ffffff'
        }
        
        self.root.configure(bg=self.colors['bg'])
    
    def setup_styles(self):
        """Setup custom styles for ttk widgets"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure button styles
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))
        
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_hover'])])
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        style.configure('Card.TFrame',
                       background=self.colors['card'],
                       relief='solid',
                       borderwidth=1)
    
    def load_config(self):
        """Load configuration from file"""
        config_file = Path(__file__).parent / "config" / "settings.json"
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except:
            self.config = {
                "urls": [
                    {"id": "1", "name": "Gmail", "url": "https://mail.google.com", "category": "Email", "enabled": True},
                    {"id": "2", "name": "GitHub", "url": "https://github.com", "category": "Development", "enabled": True},
                    {"id": "3", "name": "Slack", "url": "https://slack.com", "category": "Communication", "enabled": True},
                    {"id": "4", "name": "LinkedIn", "url": "https://linkedin.com", "category": "Social", "enabled": True},
                    {"id": "5", "name": "Calendar", "url": "https://calendar.google.com", "category": "Productivity", "enabled": True}
                ],
                "applications": [
                    {"id": "1", "name": "VS Code", "path": "code", "category": "Development", "enabled": True},
                    {"id": "2", "name": "Terminal", "path": "wt", "category": "Development", "enabled": True},
                    {"id": "3", "name": "Notepad", "path": "notepad", "category": "Productivity", "enabled": True}
                ]
            }
    
    def create_widgets(self):
        """Create all UI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        self.create_header(main_frame)
        
        # Content area with notebook
        self.create_content_area(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """Create the header section"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title
        title_label = tk.Label(header_frame, 
                              text="🌅 Morning Office Automation",
                              font=('Segoe UI', 24, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['bg'])
        title_label.pack(side=tk.LEFT)
        
        # Main launch button
        self.launch_btn = ttk.Button(header_frame,
                                    text="🚀 Start Morning Routine",
                                    style='Primary.TButton',
                                    command=self.launch_routine)
        self.launch_btn.pack(side=tk.RIGHT)
        
        # Quick launch button
        quick_btn = ttk.Button(header_frame,
                              text="⚡ Quick Launch",
                              style='Success.TButton',
                              command=self.quick_launch)
        quick_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def create_content_area(self, parent):
        """Create the main content area with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Dashboard tab
        self.create_dashboard_tab()
        
        # URLs tab
        self.create_urls_tab()
        
        # Applications tab
        self.create_apps_tab()
        
        # Timing tab
        self.create_timing_tab()
    
    def create_dashboard_tab(self):
        """Create the dashboard tab"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Stats cards
        stats_frame = ttk.Frame(dashboard_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # URL count card
        self.create_stat_card(stats_frame, "URLs Configured", 
                             len([u for u in self.config['urls'] if u.get('enabled', True)]),
                             "🌐", 0, 0)
        
        # Apps count card
        self.create_stat_card(stats_frame, "Applications", 
                             len([a for a in self.config['applications'] if a.get('enabled', True)]),
                             "🚀", 0, 1)
        
        # Session status card
        self.session_card = self.create_stat_card(stats_frame, "Session Status", 
                                                 "Inactive", "⏱️", 0, 2)
        
        # Quick actions
        actions_frame = ttk.LabelFrame(dashboard_frame, text="Quick Actions", padding=15)
        actions_frame.pack(fill=tk.X, pady=(0, 20))
        
        actions_grid = ttk.Frame(actions_frame)
        actions_grid.pack(fill=tk.X)
        
        # Individual launch buttons
        ttk.Button(actions_grid, text="📧 Open Email", 
                  command=lambda: self.launch_category("Email")).grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="💻 Dev Tools", 
                  command=lambda: self.launch_category("Development")).grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="💬 Communication", 
                  command=lambda: self.launch_category("Communication")).grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        # Configure grid weights
        for i in range(3):
            actions_grid.columnconfigure(i, weight=1)
    
    def create_stat_card(self, parent, title, value, icon, row, col):
        """Create a statistics card"""
        card_frame = ttk.Frame(parent, style='Card.TFrame', padding=15)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # Icon and value
        icon_label = tk.Label(card_frame, text=icon, font=('Segoe UI', 20),
                             bg=self.colors['card'], fg=self.colors['primary'])
        icon_label.pack()
        
        value_label = tk.Label(card_frame, text=str(value), font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['card'], fg=self.colors['text'])
        value_label.pack()
        
        title_label = tk.Label(card_frame, text=title, font=('Segoe UI', 10),
                              bg=self.colors['card'], fg=self.colors['text_light'])
        title_label.pack()
        
        # Configure grid weights
        parent.columnconfigure(col, weight=1)
        
        return value_label
    
    def create_urls_tab(self):
        """Create the URLs management tab"""
        urls_frame = ttk.Frame(self.notebook)
        self.notebook.add(urls_frame, text="🌐 URLs")
        
        # URLs list with checkboxes
        urls_list_frame = ttk.LabelFrame(urls_frame, text="Configured URLs", padding=10)
        urls_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable frame for URLs
        canvas = tk.Canvas(urls_list_frame, bg=self.colors['card'])
        scrollbar = ttk.Scrollbar(urls_list_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Create URL checkboxes
        self.url_vars = {}
        for i, url in enumerate(self.config['urls']):
            self.create_url_checkbox(scrollable_frame, url, i)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_url_checkbox(self, parent, url, row):
        """Create a checkbox for a URL"""
        var = tk.BooleanVar(value=url.get('enabled', True))
        self.url_vars[url['id']] = var
        
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=0, sticky="ew", padx=5, pady=2)
        
        # Checkbox
        cb = ttk.Checkbutton(frame, variable=var, 
                            command=lambda: self.update_url_status(url['id'], var.get()))
        cb.pack(side=tk.LEFT, padx=(0, 10))
        
        # URL info
        info_frame = ttk.Frame(frame)
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        name_label = tk.Label(info_frame, text=url['name'], font=('Segoe UI', 11, 'bold'),
                             bg=self.colors['bg'], fg=self.colors['text'])
        name_label.pack(anchor="w")
        
        url_label = tk.Label(info_frame, text=url['url'], font=('Segoe UI', 9),
                            bg=self.colors['bg'], fg=self.colors['text_light'])
        url_label.pack(anchor="w")
        
        # Category badge
        category_label = tk.Label(frame, text=url['category'], 
                                 font=('Segoe UI', 8), 
                                 bg=self.colors['primary'], fg='white',
                                 padx=8, pady=2)
        category_label.pack(side=tk.RIGHT)
        
        parent.columnconfigure(0, weight=1)
    
    def create_apps_tab(self):
        """Create the applications tab"""
        apps_frame = ttk.Frame(self.notebook)
        self.notebook.add(apps_frame, text="🚀 Applications")
        
        # Similar to URLs tab but for applications
        apps_list_frame = ttk.LabelFrame(apps_frame, text="Configured Applications", padding=10)
        apps_list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.app_vars = {}
        for i, app in enumerate(self.config['applications']):
            self.create_app_checkbox(apps_list_frame, app, i)
    
    def create_app_checkbox(self, parent, app, row):
        """Create a checkbox for an application"""
        var = tk.BooleanVar(value=app.get('enabled', True))
        self.app_vars[app['id']] = var
        
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        
        # Checkbox
        cb = ttk.Checkbutton(frame, variable=var,
                            command=lambda: self.update_app_status(app['id'], var.get()))
        cb.pack(side=tk.LEFT, padx=(0, 10))
        
        # App info
        info_frame = ttk.Frame(frame)
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        name_label = tk.Label(info_frame, text=app['name'], font=('Segoe UI', 11, 'bold'),
                             bg=self.colors['bg'], fg=self.colors['text'])
        name_label.pack(anchor="w")
        
        path_label = tk.Label(info_frame, text=app['path'], font=('Segoe UI', 9),
                             bg=self.colors['bg'], fg=self.colors['text_light'])
        path_label.pack(anchor="w")
        
        # Category badge
        category_label = tk.Label(frame, text=app['category'], 
                                 font=('Segoe UI', 8), 
                                 bg=self.colors['success'], fg='white',
                                 padx=8, pady=2)
        category_label.pack(side=tk.RIGHT)
        
        parent.columnconfigure(0, weight=1)
    
    def create_timing_tab(self):
        """Create the timing/logging tab"""
        timing_frame = ttk.Frame(self.notebook)
        self.notebook.add(timing_frame, text="⏱️ Timing")
        
        # Session info
        session_frame = ttk.LabelFrame(timing_frame, text="Current Session", padding=10)
        session_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.session_info = tk.Label(session_frame, text="No active session",
                                    font=('Segoe UI', 12),
                                    bg=self.colors['bg'], fg=self.colors['text_light'])
        self.session_info.pack()
        
        # Activity log
        log_frame = ttk.LabelFrame(timing_frame, text="Activity Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 font=('Consolas', 9),
                                                 bg=self.colors['card'])
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Add initial log entry
        self.add_log_entry("Application started")
    
    def create_status_bar(self, parent):
        """Create the status bar"""
        self.status_bar = ttk.Frame(parent)
        self.status_bar.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = tk.Label(self.status_bar, text="Ready", 
                                    font=('Segoe UI', 9),
                                    bg=self.colors['bg'], fg=self.colors['text_light'])
        self.status_label.pack(side=tk.LEFT)
        
        # Time display
        self.time_label = tk.Label(self.status_bar, text="", 
                                  font=('Segoe UI', 9),
                                  bg=self.colors['bg'], fg=self.colors['text_light'])
        self.time_label.pack(side=tk.RIGHT)
        
        self.update_time()
    
    def update_time(self):
        """Update the time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def add_log_entry(self, message):
        """Add an entry to the activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def update_status(self, message):
        """Update the status bar"""
        self.status_label.config(text=message)
        self.add_log_entry(message)
    
    def update_url_status(self, url_id, enabled):
        """Update URL enabled status"""
        for url in self.config['urls']:
            if url['id'] == url_id:
                url['enabled'] = enabled
                self.save_config()
                self.update_status(f"URL {url['name']} {'enabled' if enabled else 'disabled'}")
                break
    
    def update_app_status(self, app_id, enabled):
        """Update application enabled status"""
        for app in self.config['applications']:
            if app['id'] == app_id:
                app['enabled'] = enabled
                self.save_config()
                self.update_status(f"Application {app['name']} {'enabled' if enabled else 'disabled'}")
                break
    
    def save_config(self):
        """Save configuration to file"""
        config_file = Path(__file__).parent / "config" / "settings.json"
        try:
            config_file.parent.mkdir(exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def launch_routine(self):
        """Launch the complete morning routine"""
        if self.session_active:
            messagebox.showinfo("Session Active", "A session is already running!")
            return
        
        self.launch_btn.config(text="🔄 Launching...", state='disabled')
        self.update_status("Starting morning routine...")
        
        # Run in separate thread to avoid blocking UI
        threading.Thread(target=self._launch_routine_thread, daemon=True).start()
    
    def _launch_routine_thread(self):
        """Launch routine in separate thread"""
        try:
            self.session_active = True
            self.session_start_time = datetime.now()
            session_id = str(uuid.uuid4())
            
            # Update UI
            self.root.after(0, lambda: self.session_card.config(text="Active"))
            self.root.after(0, lambda: self.session_info.config(
                text=f"Session started at {self.session_start_time.strftime('%H:%M:%S')}\nSession ID: {session_id[:8]}..."))
            
            # Get enabled items
            enabled_urls = [url for url in self.config['urls'] if url.get('enabled', True)]
            enabled_apps = [app for app in self.config['applications'] if app.get('enabled', True)]
            
            # Launch URLs
            for url in enabled_urls:
                self.root.after(0, lambda u=url: self.update_status(f"Opening {u['name']}..."))
                webbrowser.open(url['url'])
                time.sleep(2)
            
            # Launch applications
            for app in enabled_apps:
                self.root.after(0, lambda a=app: self.update_status(f"Launching {a['name']}..."))
                try:
                    if platform.system() == "Windows":
                        subprocess.Popen(app['path'], shell=True)
                    else:
                        subprocess.Popen([app['path']])
                    time.sleep(2)
                except Exception as e:
                    self.root.after(0, lambda a=app, e=e: self.update_status(f"Failed to launch {a['name']}: {e}"))
            
            # Complete
            self.root.after(0, lambda: self.update_status(
                f"Morning routine completed! Opened {len(enabled_urls)} URLs and {len(enabled_apps)} applications"))
            
        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"Error during launch: {e}"))
        finally:
            self.root.after(0, lambda: self.launch_btn.config(text="🚀 Start Morning Routine", state='normal'))
    
    def quick_launch(self):
        """Quick launch without session tracking"""
        self.update_status("Quick launching...")
        threading.Thread(target=self._quick_launch_thread, daemon=True).start()
    
    def _quick_launch_thread(self):
        """Quick launch in separate thread"""
        try:
            enabled_urls = [url for url in self.config['urls'] if url.get('enabled', True)]
            enabled_apps = [app for app in self.config['applications'] if app.get('enabled', True)]
            
            # Launch everything quickly
            for url in enabled_urls:
                webbrowser.open(url['url'])
            
            for app in enabled_apps:
                try:
                    if platform.system() == "Windows":
                        subprocess.Popen(app['path'], shell=True)
                    else:
                        subprocess.Popen([app['path']])
                except:
                    pass
            
            self.root.after(0, lambda: self.update_status(
                f"Quick launch completed! {len(enabled_urls)} URLs + {len(enabled_apps)} apps"))
            
        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"Quick launch error: {e}"))
    
    def launch_category(self, category):
        """Launch items from a specific category"""
        self.update_status(f"Launching {category} items...")
        threading.Thread(target=self._launch_category_thread, args=(category,), daemon=True).start()
    
    def _launch_category_thread(self, category):
        """Launch category items in separate thread"""
        try:
            count = 0
            
            # Launch URLs in category
            for url in self.config['urls']:
                if url.get('category') == category and url.get('enabled', True):
                    webbrowser.open(url['url'])
                    count += 1
            
            # Launch apps in category
            for app in self.config['applications']:
                if app.get('category') == category and app.get('enabled', True):
                    try:
                        if platform.system() == "Windows":
                            subprocess.Popen(app['path'], shell=True)
                        else:
                            subprocess.Popen([app['path']])
                        count += 1
                    except:
                        pass
            
            self.root.after(0, lambda: self.update_status(f"Launched {count} {category} items"))
            
        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"Category launch error: {e}"))
    
    def run(self):
        """Start the application"""
        self.update_status("Application ready")
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        app = ModernAutomationUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start application: {e}")

if __name__ == "__main__":
    main()
