{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () {\n      return 7;\n    }\n  }).a !== 7;\n});", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "fails", "createElement", "module", "exports", "Object", "defineProperty", "get", "a"], "sources": ["C:/Users/<USER>/Desktop/acf-automatcion/frontend/node_modules/core-js-pure/internals/ie8-dom-define.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a !== 7;\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIE,aAAa,GAAGF,OAAO,CAAC,sCAAsC,CAAC;;AAEnE;AACAG,MAAM,CAACC,OAAO,GAAG,CAACL,WAAW,IAAI,CAACE,KAAK,CAAC,YAAY;EAClD;EACA,OAAOI,MAAM,CAACC,cAAc,CAACJ,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE;IACtDK,GAAG,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC;IAAE;EAC/B,CAAC,CAAC,CAACC,CAAC,KAAK,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}