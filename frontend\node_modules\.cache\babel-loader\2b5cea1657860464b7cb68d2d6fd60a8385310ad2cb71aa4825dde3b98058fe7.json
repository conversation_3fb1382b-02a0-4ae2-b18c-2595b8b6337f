{"ast": null, "code": "/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6a2 2 0 0 0-3.414-1.414l-6 6a2 2 0 0 0 0 2.828l6 6A2 2 0 0 0 12 18z\",\n  key: \"2a1g8i\"\n}], [\"path\", {\n  d: \"M22 6a2 2 0 0 0-3.414-1.414l-6 6a2 2 0 0 0 0 2.828l6 6A2 2 0 0 0 22 18z\",\n  key: \"rg3s36\"\n}]];\nconst Rewind = createLucideIcon(\"rewind\", __iconNode);\nexport { __iconNode, Rewind as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Rewind", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\acf-automatcion\\frontend\\node_modules\\lucide-react\\src\\icons\\rewind.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M12 6a2 2 0 0 0-3.414-1.414l-6 6a2 2 0 0 0 0 2.828l6 6A2 2 0 0 0 12 18z', key: '2a1g8i' },\n  ],\n  [\n    'path',\n    { d: 'M22 6a2 2 0 0 0-3.414-1.414l-6 6a2 2 0 0 0 0 2.828l6 6A2 2 0 0 0 22 18z', key: 'rg3s36' },\n  ],\n];\n\n/**\n * @component @name Rewind\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNmEyIDIgMCAwIDAtMy40MTQtMS40MTRsLTYgNmEyIDIgMCAwIDAgMCAyLjgyOGw2IDZBMiAyIDAgMCAwIDEyIDE4eiIgLz4KICA8cGF0aCBkPSJNMjIgNmEyIDIgMCAwIDAtMy40MTQtMS40MTRsLTYgNmEyIDIgMCAwIDAgMCAyLjgyOGw2IDZBMiAyIDAgMCAwIDIyIDE4eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/rewind\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rewind = createLucideIcon('rewind', __iconNode);\n\nexport default Rewind;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,yEAA2E;EAAAC,GAAA,EAAK;AAAS,EAChG,EACA,CACE,QACA;EAAED,CAAA,EAAG,yEAA2E;EAAAC,GAAA,EAAK;AAAS,GAElG;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}